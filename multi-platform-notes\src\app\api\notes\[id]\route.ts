import { NextResponse } from "next/server";
import { notes } from "@/data/notes";

// params 会包含 URL 中的动态部分，比如 { id: '1' }
export async function GET(request: Request, { params }: { params: { id: string } }) {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  const noteId = parseInt(params.id, 10);
  const note = notes.find((n) => n.id === noteId);

  if (note) {
    return NextResponse.json(note);
  } else {
    return NextResponse.json({ error: "Note not found" }, { status: 404 });
  }
}